Using Python 3.12.11 environment at: unsloth-bw/.venv
accelerate==1.8.1
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
airportsdata==20250622
annotated-types==0.7.0
anyio==4.9.0
astor==0.8.1
asttokens==3.0.0
attrs==25.3.0
bitsandbytes==0.46.0
blake3==1.0.5
cachetools==6.1.0
certifi==2025.6.15
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
comm==0.2.2
compressed-tensors==0.10.1
cupy-cuda12x==13.4.1
cut-cross-entropy==25.1.1
datasets==3.6.0
debugpy==1.8.14
decorator==5.2.1
depyf==0.18.0
diffusers==0.34.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docstring-parser==0.16
einops==0.8.1
email-validator==2.2.0
executing==2.2.0
fastapi==0.115.14
fastapi-cli==0.0.7
fastrlock==0.8.3
filelock==3.18.0
frozenlist==1.7.0
fsspec==2025.5.1
gguf==0.17.1
h11==0.16.0
hf-transfer==0.1.9
hf-xet==1.1.5
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.33.1
idna==3.10
importlib-metadata==8.7.0
interegular==0.3.3
ipykernel==6.29.5
ipython==9.3.0
ipython-pygments-lexers==1.1.1
jedi==0.19.2
jinja2==3.1.6
jiter==0.10.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter-client==8.6.3
jupyter-core==5.8.1
lark==1.2.2
llguidance==0.7.30
llvmlite==0.44.0
lm-format-enforcer==0.10.11
markdown-it-py==3.0.0
markupsafe==3.0.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mistral-common==1.6.2
mpmath==1.3.0
msgpack==1.1.1
msgspec==0.19.0
multidict==6.5.1
multiprocess==0.70.16
nest-asyncio==1.6.0
networkx==3.5
ninja==********
numba==0.61.2
numpy==2.2.0
nvidia-cublas-cu12==*********
nvidia-cuda-cupti-cu12==12.8.57
nvidia-cuda-nvrtc-cu12==12.8.61
nvidia-cuda-runtime-cu12==12.8.57
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-cufile-cu12==*********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==*********
nvidia-cusparse-cu12==*********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.8.61
nvidia-nvtx-cu12==12.8.55
openai==1.92.2
opencv-python-headless==*********
outlines==0.1.11
outlines-core==0.1.26
packaging==25.0
pandas==2.3.0
parso==0.8.4
partial-json-parser==*******.post6
peft==0.15.2
pexpect==4.9.0
pillow==11.2.1
pip==25.1.1
platformdirs==4.3.8
prometheus-client==0.22.1
prometheus-fastapi-instrumentator==7.1.0
prompt-toolkit==3.0.51
propcache==0.3.2
protobuf==3.20.3
psutil==7.0.0
ptyprocess==0.7.0
pure-eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==20.0.0
pybase64==1.4.1
pycountry==24.6.1
pydantic==2.11.7
pydantic-core==2.33.2
pygments==2.19.2
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.2
pyyaml==6.0.2
pyzmq==27.0.0
ray==2.47.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rich==14.0.0
rich-toolkit==0.14.7
rpds-py==0.25.1
safetensors==0.5.3
scipy==1.16.0
sentencepiece==0.2.0
setuptools==80.9.0
shellingham==1.5.4
shtab==1.7.2
six==1.17.0
sniffio==1.3.1
stack-data==0.6.3
starlette==0.46.2
sympy==1.14.0
tiktoken==0.9.0
tokenizers==0.21.2
torch==2.7.0+cu128
torchaudio==2.7.0+cu128
torchvision==0.22.0+cu128
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.4
triton==3.3.1
trl==0.19.0
typeguard==4.4.4
typer==0.16.0
typing-extensions==4.14.0
typing-inspection==0.4.1
tyro==0.9.24
tzdata==2025.2
unsloth==2025.6.8
unsloth-zoo==2025.6.5
urllib3==2.5.0
uvicorn==0.34.3
uvloop==0.21.0
vllm==0.9.2.dev280+g04e1642e3
watchfiles==1.1.0
wcwidth==0.2.13
websockets==15.0.1
wheel==0.45.1
xformers==0.0.32+ff490c3.d20250626
xgrammar==0.1.19
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
